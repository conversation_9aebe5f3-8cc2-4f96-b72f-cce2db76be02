// FiveM Loading Screen Script
// Handles progress tracking and auto-closing functionality

class LoadingScreen {
    constructor() {
        this.progressFill = document.getElementById('progressFill');
        this.progressGlow = document.querySelector('.progress-glow');
        this.progressText = document.getElementById('progressText');
        this.progressPercent = document.getElementById('progressPercent');
        this.loadingContainer = document.querySelector('.loading-container');
        
        this.currentProgress = 0;
        this.targetProgress = 0;
        this.isComplete = false;
        
        this.loadingStages = [
            { progress: 10, text: 'Initializing...' },
            { progress: 25, text: 'Loading resources...' },
            { progress: 40, text: 'Connecting to server...' },
            { progress: 55, text: 'Downloading assets...' },
            { progress: 70, text: 'Loading world...' },
            { progress: 85, text: 'Spawning player...' },
            { progress: 95, text: 'Finalizing...' },
            { progress: 100, text: 'Complete!' }
        ];
        
        this.currentStageIndex = 0;
        
        this.init();
    }
    
    init() {
        // Start the loading simulation
        this.startLoadingSimulation();
        
        // Listen for FiveM events if available
        this.setupFiveMListeners();
        
        // Start progress animation loop
        this.animateProgress();
        
        // Auto-close check
        this.startAutoCloseCheck();
    }
    
    startLoadingSimulation() {
        // Simulate loading progress with realistic timing
        const simulateStage = () => {
            if (this.currentStageIndex < this.loadingStages.length && !this.isComplete) {
                const stage = this.loadingStages[this.currentStageIndex];
                this.setProgress(stage.progress, stage.text);
                this.currentStageIndex++;
                
                // Random delay between stages (1-3 seconds)
                const delay = Math.random() * 2000 + 1000;
                setTimeout(simulateStage, delay);
            }
        };
        
        // Start simulation after a short delay
        setTimeout(simulateStage, 500);
    }
    
    setupFiveMListeners() {
        // Listen for FiveM loading events
        if (typeof window.addEventListener !== 'undefined') {
            // Custom FiveM events
            window.addEventListener('message', (event) => {
                const data = event.data;
                
                if (data.type === 'loadProgress') {
                    this.setProgress(data.progress, data.text || 'Loading...');
                }
                
                if (data.type === 'loadComplete') {
                    this.completeLoading();
                }
            });
        }
        
        // Check for FiveM specific functions
        if (typeof GetPlayerName !== 'undefined') {
            this.checkFiveMStatus();
        }
    }
    
    checkFiveMStatus() {
        const checkInterval = setInterval(() => {
            try {
                // Check if player is loaded
                if (typeof GetPlayerName !== 'undefined' && GetPlayerName(PlayerId()) !== '') {
                    this.completeLoading();
                    clearInterval(checkInterval);
                }
                
                // Alternative check using NetworkIsPlayerActive
                if (typeof NetworkIsPlayerActive !== 'undefined' && NetworkIsPlayerActive(PlayerId())) {
                    this.completeLoading();
                    clearInterval(checkInterval);
                }
            } catch (error) {
                // Continue checking if functions aren't available yet
            }
        }, 1000);
        
        // Fallback timeout (30 seconds max)
        setTimeout(() => {
            clearInterval(checkInterval);
            if (!this.isComplete) {
                this.completeLoading();
            }
        }, 30000);
    }
    
    setProgress(progress, text) {
        this.targetProgress = Math.min(Math.max(progress, 0), 100);
        
        if (text) {
            this.progressText.textContent = text;
        }
        
        // Add some visual feedback for progress changes
        this.progressFill.style.boxShadow = `
            0 0 15px rgba(0, 255, 255, 0.8),
            0 0 25px rgba(0, 255, 255, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.3)
        `;
        
        setTimeout(() => {
            this.progressFill.style.boxShadow = `
                0 0 10px rgba(0, 255, 255, 0.6),
                0 0 20px rgba(0, 255, 255, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3)
            `;
        }, 300);
    }
    
    animateProgress() {
        const animate = () => {
            if (Math.abs(this.currentProgress - this.targetProgress) > 0.1) {
                // Smooth progress animation
                this.currentProgress += (this.targetProgress - this.currentProgress) * 0.1;
                
                // Update progress bar
                this.progressFill.style.width = `${this.currentProgress}%`;
                this.progressGlow.style.width = `${this.currentProgress}%`;
                this.progressPercent.textContent = `${Math.round(this.currentProgress)}%`;
            }
            
            if (!this.isComplete) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    completeLoading() {
        if (this.isComplete) return;
        
        this.isComplete = true;
        this.setProgress(100, 'Complete!');
        
        // Wait for final animation to complete
        setTimeout(() => {
            this.fadeOut();
        }, 1000);
    }
    
    fadeOut() {
        this.loadingContainer.classList.add('fade-out');
        
        // Hide the loading screen after fade animation
        setTimeout(() => {
            document.body.style.display = 'none';
            
            // Notify FiveM that loading is complete
            if (typeof ShutdownLoadingScreen !== 'undefined') {
                ShutdownLoadingScreen();
            }
            
            // Alternative method for closing loading screen
            if (typeof SendNUIMessage !== 'undefined') {
                SendNUIMessage({
                    type: 'closeLoadingScreen'
                });
            }
            
            // Fallback method
            if (window.invokeNative) {
                window.invokeNative('SHUTDOWN_LOADING_SCREEN');
            }
        }, 1000);
    }
    
    startAutoCloseCheck() {
        // Additional safety check - auto close after maximum time
        setTimeout(() => {
            if (!this.isComplete) {
                console.log('Auto-closing loading screen after timeout');
                this.completeLoading();
            }
        }, 45000); // 45 seconds maximum
    }
}

// Initialize loading screen when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new LoadingScreen();
});

// Fallback initialization
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new LoadingScreen();
    });
} else {
    new LoadingScreen();
}

// Global functions for FiveM integration
window.setLoadingProgress = function(progress, text) {
    if (window.loadingScreen) {
        window.loadingScreen.setProgress(progress, text);
    }
};

window.completeLoading = function() {
    if (window.loadingScreen) {
        window.loadingScreen.completeLoading();
    }
};

// Store reference for global access
window.addEventListener('load', () => {
    if (!window.loadingScreen) {
        window.loadingScreen = new LoadingScreen();
    }
});