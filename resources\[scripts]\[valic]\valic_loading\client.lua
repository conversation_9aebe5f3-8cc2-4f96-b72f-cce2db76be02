local isLoadingScreenActive = true
local currentProgress = 0.0
local loadingSteps = {
    {event = "INIT_CORE", progress = 0.05, text = "Inicializace..."},
    {event = "INIT_BEFORE_MAP_LOADED", progress = 0.15, text = "Načítám mapu..."},
    {event = "INIT_AFTER_MAP_LOADED", progress = 0.25, text = "Mapa načtena..."},
    {event = "INIT_SESSION", progress = 0.35, text = "Připojuji session..."},
    {event = "INIT_SESSION_BROWSER", progress = 0.45, text = "Načítám prohlížeč..."},
    {event = "INIT_SESSION_DETAIL", progress = 0.55, text = "Načítám detaily..."},
    {event = "INIT_SESSION_FINALIZE", progress = 0.65, text = "Dokončuji session..."},
    {event = "INIT_BEFORE_MAP_LOADED", progress = 0.75, text = "Připravuji svět..."},
    {event = "INIT_AFTER_MAP_LOADED", progress = 0.85, text = "Svět připraven..."},
    {event = "INIT_SESSION", progress = 0.95, text = "Dokončuji..."}
}

-- Funkce pro aktualizaci progress baru
function UpdateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        currentProgress = progress
        SendNUIMessage({
            eventName = 'loadProgress',
            loadFraction = progress,
            loadingText = text or "Načítám..."
        })
    end
end

-- Handler pro loading eventy
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        UpdateLoadingProgress(0.0, "Spouštím loading screen...")
    end
end)

-- Sledování loading eventů
local loadingEventIndex = 1
AddEventHandler('onClientMapStart', function()
    if loadingEventIndex <= #loadingSteps then
        local step = loadingSteps[loadingEventIndex]
        UpdateLoadingProgress(step.progress, step.text)
        loadingEventIndex = loadingEventIndex + 1
    end
end)

-- Hlavní loading thread
Citizen.CreateThread(function()
    local startTime = GetGameTimer()
    local maxLoadTime = 30000 -- 30 sekund maximum

    while isLoadingScreenActive do
        Citizen.Wait(100)

        -- Kontrola, zda je hráč spawnutý
        if NetworkIsPlayerActive(PlayerId()) then
            -- Počkáme ještě chvilku pro jistotu
            Citizen.Wait(2000)

            -- Zkontrolujeme, zda existuje multicharacter systém
            if GetResourceState('esx_multicharacter') == 'started' or
               GetResourceState('qb-multicharacter') == 'started' or
               GetResourceState('multicharacter') == 'started' then
                -- Počkáme na načtení multicharacter
                Citizen.Wait(1000)
            end

            -- Dokončíme loading
            UpdateLoadingProgress(1.0, "Vítejte v Diverse RP!")
            Citizen.Wait(1000)

            -- Vypneme loading screen
            isLoadingScreenActive = false
            ShutdownLoadingScreenNui()
            return
        end

        -- Fallback - pokud loading trvá příliš dlouho
        if GetGameTimer() - startTime > maxLoadTime then
            UpdateLoadingProgress(1.0, "Dokončuji...")
            Citizen.Wait(1000)
            isLoadingScreenActive = false
            ShutdownLoadingScreenNui()
            return
        end

        -- Postupné zvyšování progress baru pokud nejsou eventy
        if currentProgress < 0.9 then
            currentProgress = currentProgress + 0.005
            UpdateLoadingProgress(currentProgress, "Načítám...")
        end
    end
end)

-- Event pro manuální ukončení loading screenu
RegisterNetEvent('valic_loading:hideLoadingScreen')
AddEventHandler('valic_loading:hideLoadingScreen', function()
    if isLoadingScreenActive then
        isLoadingScreenActive = false
        ShutdownLoadingScreenNui()
    end
end)
