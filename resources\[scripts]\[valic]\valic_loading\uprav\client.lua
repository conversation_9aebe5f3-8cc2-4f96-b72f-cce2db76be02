local isLoadingScreenActive = false -- <PERSON><PERSON><PERSON><PERSON><PERSON> s false, aktivujeme pouze při prvním načtení
local currentProgress = 0.0
local hasPlayerSpawned = false
local loadingSteps = {
    {event = "INIT_CORE", progress = 0.05, text = "Inicializace..."},
    {event = "INIT_BEFORE_MAP_LOADED", progress = 0.15, text = "Načítám mapu..."},
    {event = "INIT_AFTER_MAP_LOADED", progress = 0.25, text = "Mapa načtena..."},
    {event = "INIT_SESSION", progress = 0.35, text = "Připojuji session..."},
    {event = "INIT_SESSION_BROWSER", progress = 0.45, text = "Načítám prohlížeč..."},
    {event = "INIT_SESSION_DETAIL", progress = 0.55, text = "Načítám detaily..."},
    {event = "INIT_SESSION_FINALIZE", progress = 0.65, text = "Dokončuji session..."},
    {event = "INIT_BEFORE_MAP_LOADED", progress = 0.75, text = "Připravuji svět..."},
    {event = "INIT_AFTER_MAP_LOADED", progress = 0.85, text = "Svět připraven..."},
    {event = "INIT_SESSION", progress = 0.95, text = "Dokončuji..."}
}

function UpdateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        currentProgress = progress
        SendNUIMessage({
            eventName = 'loadProgress',
            loadFraction = progress,
            loadingText = text or "Načítám..."
        })
    end
end

AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Pouze pokud hráč ještě není spawnutý (první načtení)
        if not NetworkIsPlayerActive(PlayerId()) and not hasPlayerSpawned then
            isLoadingScreenActive = true
            UpdateLoadingProgress(0.0, "Spouštím loading screen...")
        else
            -- Pokud je hráč už spawnutý, loading screen nepotřebujeme
            isLoadingScreenActive = false
        end
    end
end)

local loadingEventIndex = 1
AddEventHandler('onClientMapStart', function()
    if loadingEventIndex <= #loadingSteps then
        local step = loadingSteps[loadingEventIndex]
        UpdateLoadingProgress(step.progress, step.text)
        loadingEventIndex = loadingEventIndex + 1
    end
end)

Citizen.CreateThread(function()
    local startTime = GetGameTimer()
    local maxLoadTime = 30000 

    while isLoadingScreenActive do
        Citizen.Wait(100)

        if NetworkIsPlayerActive(PlayerId()) then
            hasPlayerSpawned = true
            Citizen.Wait(2000)

            if GetResourceState('esx_multicharacter') == 'started' or
               GetResourceState('qb-multicharacter') == 'started' or
               GetResourceState('multicharacter') == 'started' or
               GetResourceState('okokMulticharacter') == 'started' then
                Citizen.Wait(2000)
            end

            UpdateLoadingProgress(1.0, "Vítejte v Diverse RP!")
            Citizen.Wait(1000)

            isLoadingScreenActive = false
            ShutdownLoadingScreenNui()
            return
        end

        if GetGameTimer() - startTime > maxLoadTime then
            UpdateLoadingProgress(1.0, "Dokončuji...")
            Citizen.Wait(1000)
            isLoadingScreenActive = false
            ShutdownLoadingScreenNui()
            return
        end

        if currentProgress < 0.9 then
            currentProgress = currentProgress + 0.005
            UpdateLoadingProgress(currentProgress, "Načítám...")
        end
    end
end)

RegisterNetEvent('valic_loading:hideLoadingScreen')
AddEventHandler('valic_loading:hideLoadingScreen', function()
    if isLoadingScreenActive then
        hasPlayerSpawned = true
        isLoadingScreenActive = false
        ShutdownLoadingScreenNui()
    end
end)

RegisterNetEvent('okokMulticharacter:spawnPlayer')
AddEventHandler('okokMulticharacter:spawnPlayer', function()
    if isLoadingScreenActive then
        hasPlayerSpawned = true
        UpdateLoadingProgress(1.0, "Spawning postava...")
        Citizen.Wait(1000)
        isLoadingScreenActive = false
        ShutdownLoadingScreenNui()
    end
end)

RegisterNetEvent('okokMulticharacter:characterSelected')
AddEventHandler('okokMulticharacter:characterSelected', function()
    if isLoadingScreenActive then
        UpdateLoadingProgress(0.95, "Načítám postavu...")
    end
end)
