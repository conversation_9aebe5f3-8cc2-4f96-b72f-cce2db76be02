* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    overflow: hidden;
    font-family: 'Orbitron', monospace;
    background: #000;
}

.loading-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 128, 255, 0.1) 0%, transparent 50%);
    animation: backgroundPulse 4s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

.content {
    position: relative;
    z-index: 10;
    text-align: center;
    width: 100%;
    max-width: 600px;
    padding: 0 40px;
}

.server-name {
    position: relative;
    margin-bottom: 80px;
}

.server-name h1 {
    font-size: 4.5rem;
    font-weight: 900;
    color: #00ffff;
    text-shadow: 
        0 0 10px #00ffff,
        0 0 20px #00ffff,
        0 0 40px #00ffff,
        0 0 80px #00ffff;
    letter-spacing: 8px;
    animation: titleGlow 2s ease-in-out infinite alternate;
    position: relative;
    z-index: 2;
}

.server-name-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: glowPulse 3s ease-in-out infinite;
    z-index: 1;
}

@keyframes titleGlow {
    0% {
        text-shadow: 
            0 0 10px #00ffff,
            0 0 20px #00ffff,
            0 0 40px #00ffff,
            0 0 80px #00ffff;
    }
    100% {
        text-shadow: 
            0 0 20px #00ffff,
            0 0 30px #00ffff,
            0 0 60px #00ffff,
            0 0 100px #00ffff;
    }
}

@keyframes glowPulse {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.6;
    }
}

.progress-section {
    width: 100%;
}

.progress-container {
    position: relative;
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(0, 255, 255, 0.2);
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 100%;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #0080ff 0%, #00ffff 50%, #0080ff 100%);
    border-radius: 10px;
    transition: width 0.3s ease-out;
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.6),
        0 0 20px rgba(0, 255, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: progressShimmer 2s linear infinite;
}

.progress-glow {
    position: absolute;
    top: -2px;
    left: 0;
    height: calc(100% + 4px);
    width: 0%;
    background: rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    filter: blur(4px);
    transition: width 0.3s ease-out;
}

@keyframes progressShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 400;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

#progressText {
    color: #00ffff;
    font-weight: 700;
}

#progressPercent {
    color: #ffffff;
    font-weight: 400;
}

.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ffff;
    border-radius: 50%;
    box-shadow: 0 0 6px #00ffff;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 6s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    animation-duration: 10s;
}

.particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 7s;
}

.particle:nth-child(5) {
    top: 70%;
    left: 50%;
    animation-delay: 3s;
    animation-duration: 9s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-20px) translateX(10px);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) translateX(-10px);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-30px) translateX(5px);
        opacity: 0.9;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .server-name h1 {
        font-size: 3rem;
        letter-spacing: 4px;
    }
    
    .content {
        padding: 0 20px;
    }
    
    .progress-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .server-name h1 {
        font-size: 2.5rem;
        letter-spacing: 2px;
    }
    
    .server-name {
        margin-bottom: 60px;
    }
}

/* Loading Animation */
.loading-container.fade-out {
    animation: fadeOut 1s ease-out forwards;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(1.05);
    }
}