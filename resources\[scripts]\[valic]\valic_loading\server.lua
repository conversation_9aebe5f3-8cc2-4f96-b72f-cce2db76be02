-- Server-side script pro valic_loading
-- Zajišťuje správnou synchronizaci loading screenu

-- Event pro skrytí loading screenu ze serveru
RegisterNetEvent('valic_loading:requestHideLoadingScreen')
AddEventHandler('valic_loading:requestHideLoadingScreen', function()
    local source = source
    TriggerClientEvent('valic_loading:hideLoadingScreen', source)
end)

-- Event pro kontrolu stavu hráče
RegisterNetEvent('valic_loading:playerReady')
AddEventHandler('valic_loading:playerReady', function()
    local source = source
    print(('[valic_loading] Hráč %s je připraven, skrývám loading screen'):format(GetPlayerName(source)))
    
    -- Počkáme chvilku a pak skryjeme loading screen
    Citizen.SetTimeout(2000, function()
        TriggerClientEvent('valic_loading:hideLoadingScreen', source)
    end)
end)

-- <PERSON>k<PERSON> skrytí loading screenu po připojení hráče
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print(('[valic_loading] Hráč %s se připojuje...'):format(name))
end)

AddEventHandler('playerJoining', function(source)
    print(('[valic_loading] Hráč %s se připojil'):format(GetPlayerName(source)))
end)

-- Kontrola multicharacter systémů
Citizen.CreateThread(function()
    -- Zkontroluj dostupné multicharacter systémy
    local multicharacterSystems = {
        'esx_multicharacter',
        'qb-multicharacter', 
        'multicharacter',
        'fivem-appearance'
    }
    
    for _, resourceName in ipairs(multicharacterSystems) do
        if GetResourceState(resourceName) == 'started' then
            print(('[valic_loading] Detekován multicharacter systém: %s'):format(resourceName))
        end
    end
end)
