    RegisterNetEvent('valic_loading:requestHideLoadingScreen')
AddEventHandler('valic_loading:requestHideLoadingScreen', function()
    local source = source
    TriggerClientEvent('valic_loading:hideLoadingScreen', source)
end)

RegisterNetEvent('valic_loading:playerReady')
AddEventHandler('valic_loading:playerReady', function()
    local source = source
    print(('[valic_loading] Hráč %s je př<PERSON>raven, skrývám loading screen'):format(GetPlayerName(source)))
    
    Citizen.SetTimeout(2000, function()
        TriggerClientEvent('valic_loading:hideLoadingScreen', source)
    end)
end)

AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print(('[valic_loading] Hráč %s se připojuje...'):format(name))
end)

AddEventHandler('playerJoining', function(source)
    print(('[valic_loading] Hr<PERSON><PERSON> %s se připojil'):format(GetPlayerName(source)))
end)

Citizen.CreateThread(function()

    local multicharacterSystems = {
        'esx_multicharacter',
        'qb-multicharacter',
        'multicharacter',
        'okokMulticharacter',
        'fivem-appearance'
    }

    for _, resourceName in ipairs(multicharacterSystems) do
        if GetResourceState(resourceName) == 'started' then
            print(('[valic_loading] Detekován multicharacter systém: %s'):format(resourceName))
        end
    end
end)

AddEventHandler('okokMulticharacter:playerLoaded', function()
    local source = source
    print(('[valic_loading] okokMulticharacter načten pro hráče %s'):format(GetPlayerName(source)))

    Citizen.SetTimeout(1500, function()
        TriggerClientEvent('valic_loading:hideLoadingScreen', source)
    end)
end)
