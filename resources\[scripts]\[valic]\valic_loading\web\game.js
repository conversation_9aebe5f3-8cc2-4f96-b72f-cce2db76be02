const progressBar = document.getElementById('progressBar');
const progressPercentage = document.getElementById('progressPercentage');
const loadingText = document.getElementById('loadingText');

const loadingStates = {
    0: "Inicializace...",
    10: "Načítám scripty...",
    25: "Načítám vozidla...",
    40: "Synchronizuji počasí...",
    55: "Připravuji postavu...",
    70: "Už to skoro bude...",
    85: "Připojuji voice chat...",
    95: "Dokončuji...",
};

function updateProgress(percentage) {
    progressBar.style.width = `${percentage}%`;
    progressPercentage.textContent = `${Math.round(percentage)}%`;

    let currentText = loadingStates[0];
    for (const perc in loadingStates) {
        if (percentage >= perc) {
            currentText = loadingStates[perc];
        }
    }
    loadingText.textContent = currentText;
}

window.addEventListener('message', function(e) {
    if (e.data.eventName === 'loadProgress') {
        updateProgress(e.data.loadFraction * 100);
    }
});

if (typeof GetParentResourceName === 'undefined') {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 5;
        if (progress > 100) {
            progress = 100;
            clearInterval(interval);
        }
        updateProgress(progress);
    }, 200);
}
