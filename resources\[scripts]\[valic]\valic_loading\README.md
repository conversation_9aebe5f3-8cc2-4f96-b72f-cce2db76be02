# DVRP Loading Screen

A modern, minimal FiveM loading screen with dark theme, neon accents, and auto-closing functionality.

## Features

- ✨ **Modern Dark Theme** with neon cyan accents
- 🎯 **Minimal Design** showing only server name and progress
- 📊 **Real-time Progress Bar** with smooth animations
- 🔄 **Auto-closing** when FiveM finishes loading
- 📱 **Responsive Design** for all screen sizes
- ⚡ **Smooth Animations** and visual effects

## Installation

1. **Download/Clone** this repository to your FiveM server's `resources` folder
2. **Rename** the folder to `valic_loading` (or your preferred name)
3. **Add to server.cfg**:
   ```
   ensure valic_loading
   ```
4. **Restart** your FiveM server

## File Structure

```
valic_loading/
├── fxmanifest.lua          # FiveM resource manifest
├── index.html              # Main loading screen HTML
├── style.css               # Modern dark theme styling
├── script.js               # Progress tracking & auto-close logic
└── README.md               # This file
```

## Customization

### Server Name
To change the server name from "DVRP":
1. Open `index.html`
2. Find line 16: `<h1>DVRP</h1>`
3. Replace "DVRP" with your server name

### Colors
To customize the neon accent color:
1. Open `style.css`
2. Find all instances of `#00ffff` (cyan)
3. Replace with your preferred color (e.g., `#ff0080` for pink)

### Loading Messages
To customize loading stage messages:
1. Open `script.js`
2. Find the `loadingStages` array (around line 18)
3. Modify the text messages as desired

## Technical Details

### Auto-closing Mechanism
The loading screen automatically closes when:
- Player is fully loaded in FiveM
- Maximum timeout is reached (45 seconds)
- FiveM sends completion signal

### Progress Tracking
- Simulates realistic loading stages
- Responds to FiveM loading events
- Smooth progress bar animations
- Real-time percentage display

### Browser Compatibility
- Modern browsers (Chrome, Firefox, Edge)
- Mobile responsive design
- Hardware acceleration for smooth animations

## Troubleshooting

### Loading Screen Won't Close
1. Check FiveM server console for errors
2. Verify `loadscreen_manual_shutdown 'yes'` in fxmanifest.lua
3. Ensure no conflicting loading screen resources

### Progress Bar Not Moving
1. Check browser console for JavaScript errors
2. Verify all files are properly loaded
3. Check network connectivity

### Visual Issues
1. Clear browser cache
2. Check CSS file is loading correctly
3. Verify font loading from Google Fonts

## Performance

- **Lightweight**: ~15KB total size
- **Optimized**: CSS animations use GPU acceleration
- **Efficient**: Minimal JavaScript overhead
- **Fast Loading**: External font loading with fallbacks

## Browser Support

| Browser | Version | Status |
|---------|---------|--------|
| Chrome  | 60+     | ✅ Full Support |
| Firefox | 55+     | ✅ Full Support |
| Edge    | 79+     | ✅ Full Support |
| Safari  | 12+     | ✅ Full Support |

## License

This project is open source. Feel free to modify and distribute.

## Credits

- **Author**: Valic
- **Server**: DVRP
- **Font**: Orbitron (Google Fonts)
- **Framework**: Vanilla HTML/CSS/JavaScript

---

**Need help?** Check the FiveM documentation or community forums for additional support.