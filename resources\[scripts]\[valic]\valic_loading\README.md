# Valic Loading Screen v2.0

Synchronizovaný loading screen pro FiveM servery s automatickým vypnutím po načtení multicharacter systému.

## Funkce

- ✅ **Synchronizace s FiveM**: Progress bar se synchronizuje s reálným načítáním FiveM klienta
- ✅ **Automatické vypnutí**: Loading screen se automaticky vypne po načtení multicharacter systému
- ✅ **Detekce multicharacter**: Podporuje ESX, QB-Core a další multicharacter systémy
- ✅ **Fallback systém**: Pokud loading trvá příliš dlouho, automaticky se vypne po 30 sekundách
- ✅ **Hudební p<PERSON>**: Integrovaný music player s vlastními songy
- ✅ **Responzivní design**: Funguje na všech rozlišeních
- ✅ **Animované pozadí**: Rotující obrázky na pozadí

## Instalace

1. Zkopírujte složku `valic_loading` do vašeho `resources` adresáře
2. Přidejte do `server.cfg`:
   ```
   ensure valic_loading
   ```
3. Restartujte server

## Podporované multicharacter systémy

- `esx_multicharacter`
- `qb-multicharacter` 
- `multicharacter`
- `fivem-appearance`

## Konfigurace

### Změna hudby
Nahraďte soubory v `web/music/` a upravte playlist v `web/index.html` (řádky 242-261).

### Změna obrázků na pozadí
Nahraďte obrázky v `web/assets/background-imgs/` a upravte seznam v `web/index.html` (řádky 193-210).

### Změna textu loading stavů
Upravte `loadingStates` v `web/game.js` (řádky 5-18).

## Manuální ovládání

### Skrytí loading screenu ze serveru
```lua
TriggerClientEvent('valic_loading:hideLoadingScreen', source)
```

### Skrytí loading screenu z klienta
```lua
TriggerEvent('valic_loading:hideLoadingScreen')
```

## Řešení problémů

### Loading screen se nevypne
1. Zkontrolujte konzoli serveru pro chybové zprávy
2. Ujistěte se, že máte `loadscreen_manual_shutdown 'yes'` v fxmanifest.lua
3. Zkontrolujte, zda je váš multicharacter systém správně spuštěn

### Progress bar nejde
1. Zkontrolujte konzoli prohlížeče (F12) pro JavaScript chyby
2. Ujistěte se, že jsou všechny soubory správně nahrané

### Hudba nehraje
1. Zkontrolujte, zda jsou MP3 soubory v `web/music/` složce
2. Některé prohlížeče blokují autoplay - klikněte na play tlačítko

## Changelog

### v2.0.0
- Přidána synchronizace s FiveM loading eventy
- Automatické vypnutí po načtení multicharacter
- Vylepšený fallback systém
- Lepší detekce multicharacter systémů

### v1.0.0
- Základní loading screen s music playerem
- Animované pozadí
- Responzivní design

## Autor

**stepan_valic** - Diverse RP

## Licence

Tento projekt je licencován pod MIT licencí.
